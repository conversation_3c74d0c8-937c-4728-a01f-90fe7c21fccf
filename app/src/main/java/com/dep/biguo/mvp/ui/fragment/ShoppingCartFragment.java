package com.dep.biguo.mvp.ui.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;
import com.dep.biguo.R;

public class ShoppingCartFragment extends Fragment {

    private RecyclerView rvCartItems;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_shopping_cart, container, false);
        rvCartItems = view.findViewById(R.id.rv_cart_items);
        // TODO: Setup RecyclerView with adapter and data
        return view;
    }
}