package com.dep.biguo.mvp.ui.adapter;

import android.graphics.Color;
import android.graphics.Paint;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.PriceSpannableUtil;

import java.util.List;

/**
 * 商城商品适配器
 */
public class MallProductAdapter extends BaseQuickAdapter<ShopBean, BaseViewHolder> {

    private static final String TAG = "MallProductAdapter";

    public MallProductAdapter(@Nullable List<ShopBean> data) {
        super(R.layout.item_mall_product, data);
        Log.d(TAG, "MallProductAdapter created with " + (data != null ? data.size() : 0) + " items");
    }

    @Override
    protected void convert(BaseViewHolder helper, ShopBean item) {
        Log.d(TAG, "convert() called for position: " + helper.getAdapterPosition() + ", item: " + (item != null ? item.getName() : "null"));

        if (item == null) {
            Log.e(TAG, "Item is null at position: " + helper.getAdapterPosition());
            return;
        }

        try {
            // 商品图片
            ImageView ivProductImage = helper.getView(R.id.ivProductImage);
            if (item.getImg() != null && !item.getImg().isEmpty()) {
                Log.d(TAG, "Loading image: " + item.getImg());
                ImageLoader.loadImage(ivProductImage, item.getImg());
            } else {
                Log.d(TAG, "No image, using placeholder");
                ivProductImage.setImageResource(R.drawable.bg_round_8_gray);
            }

            // 商品标签
            helper.setText(R.id.tvProductTag, "笔果"); // Mock data, can be replaced with actual data if available

            // 商品标题
            Log.d(TAG, "Setting title: " + item.getName());
            helper.setText(R.id.tvProductTitle, item.getName());

            // 价格
            helper.setText(R.id.tvPrice, "¥" + item.getPrice());

            // 已售数量
            helper.setText(R.id.tvSoldCount, "已售" + item.getSales() + "+");


        } catch (Exception e) {
            Log.e(TAG, "Error in convert() at position " + helper.getAdapterPosition() + ": " + e.getMessage(), e);
        }
    }
} 