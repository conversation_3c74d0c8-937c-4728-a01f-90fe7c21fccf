package com.dep.biguo.mvp.ui.activity;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import android.text.TextUtils;
import android.view.View;

import com.bigkoo.pickerview.OptionsPickerView;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.bean.AddressBean;
import com.dep.biguo.bean.PickerJsonBean;
import com.dep.biguo.databinding.AddressMsgActivityBinding;
import com.dep.biguo.di.component.DaggerAddressMsgComponent;
import com.dep.biguo.mvp.contract.AddressMsgContract;
import com.dep.biguo.mvp.presenter.AddressMsgPresenter;
import com.dep.biguo.utils.GetAssetFileJsonUtil;
import com.biguo.utils.util.KeyboardUtils;
import com.dep.biguo.widget.ToolBar;
import com.dep.biguo.widget.toolbar.NormalToolbarUtil;
import com.google.gson.Gson;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseActivity;
import com.jess.arms.di.component.AppComponent;

import org.json.JSONArray;

import java.util.ArrayList;


import static com.jess.arms.utils.Preconditions.checkNotNull;

public class AddressMsgActivity extends BaseActivity<AddressMsgPresenter> implements AddressMsgContract.View, View.OnClickListener {
    public static final String ADDRESS = "address";
    private AddressMsgActivityBinding binding;

    private ArrayList<PickerJsonBean> options1Items = new ArrayList<>();
    private ArrayList<ArrayList<String>> options2Items = new ArrayList<>();
    private ArrayList<ArrayList<ArrayList<String>>> options3Items = new ArrayList<>();
    private Thread thread;
    private static final int MSG_LOAD_DATA = 0x0001;
    private static final int MSG_LOAD_SUCCESS = 0x0002;
    private static final int MSG_LOAD_FAILED = 0x0003;

    private boolean isLoaded = false;

    private AddressBean addressBean;

    private String province;
    private String city;
    private String area;


    private Handler mHandler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case MSG_LOAD_DATA:
                    if (thread == null) {//如果已创建就不再重新创建子线程了
                        thread = new Thread(() -> {
                            // 写子线程中的操作,解析省市区数据
                            initJsonData();
                        });
                        thread.start();
                    }
                    break;

                case MSG_LOAD_SUCCESS:
                    isLoaded = true;
                    showPickerView();
                    break;

                case MSG_LOAD_FAILED:
                    showMessage("获取地区信息失败");
                    break;
            }
            return false;
        }
    });

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerAddressMsgComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.address_msg_activity);
        binding.setOnClickListener(this);
        // 添加直接的点击监听器作为备用方案
        binding.tvAddress.setOnClickListener(this);
        binding.isDefaultLayout.setOnClickListener(this);
        binding.addAddressView.setOnClickListener(this);
        
        new NormalToolbarUtil(this)
                .setCenterText("收货信息");

        return 0;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        addressBean = getIntent().getParcelableExtra(ADDRESS);

        if (addressBean != null && !TextUtils.isEmpty(addressBean.getName())) {
            province = addressBean.getProvinces();
            city = addressBean.getCity();
            area = addressBean.getArea();

            binding.etName.setText(addressBean.getName());
            binding.etPhone.setText(addressBean.getPhone());
            binding.tvAddress.setText(province + city + area);
            binding.detailAddressView.setText(addressBean.getDetail());
        }
    }



    @Override
    public void showMessage(@NonNull String message) {
        checkNotNull(message);
        ToastUtils.show(message);
    }

    @Override
    public void onClick(View view) {
        // 添加调试日志
        android.util.Log.d("AddressMsgActivity", "onClick called, view id: " + view.getId());
        
        if(view == binding.tvAddress){
            android.util.Log.d("AddressMsgActivity", "tvAddress clicked, isLoaded: " + isLoaded);
            KeyboardUtils.hideKeyboard(findViewById(R.id.tv_address));
            if (isLoaded) {
                showPickerView();
            } else {
                mHandler.sendEmptyMessage(MSG_LOAD_DATA);
            }

        }else if(view == binding.isDefaultLayout){
            android.util.Log.d("AddressMsgActivity", "isDefaultLayout clicked");
            binding.isDefaultView.setChecked(!binding.isDefaultView.isChecked());

        }else if(view == binding.addAddressView){
            android.util.Log.d("AddressMsgActivity", "addAddressView clicked");
            mPresenter.updateAddress();
        }
    }

    private void showPickerView() {// 弹出选择器

        OptionsPickerView pvOptions = new OptionsPickerView.Builder(this, new OptionsPickerView.OnOptionsSelectListener() {
                    @Override
                    public void onOptionsSelect(int options1, int options2, int options3, View v) {
                        //返回的分别是三个级别的选中位置
                        mPresenter.setPickerIndex(new int[]{options1, options2, options3});
                        //返回的分别是三个级别的选中名称
                        province = options1Items.get(options1).getPickerViewText();
                        city = options2Items.get(options1).get(options2);
                        area = options3Items.get(options1).get(options2).get(options3);
                        binding.tvAddress.setText(province + city + area);
                    }
                })
                .setTitleText("选择地区")
                .setTitleSize(16)
                .setTitleColor(Color.parseColor("#333333"))
                .setTitleBgColor(Color.parseColor("#F5F5F5"))
                .setSubCalSize(14)
                .setContentTextSize(16)
                .setTextColorCenter(Color.parseColor("#333333")) //设置选中项文字颜色
                .setTextColorOut(Color.parseColor("#999999")) //设置非选中项文字颜色
                .setDividerColor(Color.parseColor("#E0E0E0"))
                .setBgColor(Color.WHITE)
                .setSubmitColor(Color.parseColor("#E53E3E")) //确定按钮颜色，匹配原型图中的红色
                .setCancelColor(Color.parseColor("#666666")) //取消按钮颜色
                .setSubmitText("保存")
                .setCancelText("取消")
                .setLineSpacingMultiplier(2.0f)
                .isDialog(false)
                .build();

        pvOptions.setPicker(options1Items, options2Items, options3Items);//三级选择器

        int[] index = mPresenter.getPickerScrollIndex();
        pvOptions.setSelectOptions(index[0], index[1], index[2]);
        pvOptions.show();
    }

    private void initJsonData() {//解析数据

        String JsonData = new GetAssetFileJsonUtil().getJson(this, "province.json");//获取assets目录下的json文件数据

        ArrayList<PickerJsonBean> jsonBean = parseData(JsonData);//用Gson 转成实体

        /**
         * 添加省份数据
         *
         * 注意：如果是添加的JavaBean实体，则实体类需要实现 IPickerViewData 接口，
         * PickerView会通过getPickerViewText方法获取字符串显示出来。
         */
        options1Items = jsonBean;

        for (int i = 0; i < jsonBean.size(); i++) {//遍历省份
            ArrayList<String> CityList = new ArrayList<>();//该省的城市列表（第二级）
            ArrayList<ArrayList<String>> Province_AreaList = new ArrayList<>();//该省的所有地区列表（第三极）

            for (int c = 0; c < jsonBean.get(i).getChildren().size(); c++) {//遍历该省份的所有城市
                String CityName = jsonBean.get(i).getChildren().get(c).getName();
                CityList.add(CityName);//添加城市

                ArrayList<String> City_AreaList = new ArrayList<>();//该城市的所有地区列表

                //如果无地区数据，建议添加空字符串，防止数据为null 导致三个选项长度不匹配造成崩溃
                if (jsonBean.get(i).getChildren().get(c).getChildren() == null
                        || jsonBean.get(i).getChildren().get(c).getChildren().size() == 0) {
                    City_AreaList.add("");
                } else {

                    for (int d = 0; d < jsonBean.get(i).getChildren().get(c).getChildren().size(); d++) {//该城市对应地区所有数据
                        String AreaName = jsonBean.get(i).getChildren().get(c).getChildren().get(d).getName();

                        City_AreaList.add(AreaName);//添加该城市所有地区数据
                    }
                }
                Province_AreaList.add(City_AreaList);//添加该省所有地区数据
            }

            /**
             * 添加城市数据
             */
            options2Items.add(CityList);

            /**
             * 添加地区数据
             */
            options3Items.add(Province_AreaList);
        }

        //找出当前地址在选择器中的位置
        mPresenter.queryNowPickIndex(addressBean, options1Items);

        mHandler.sendEmptyMessage(MSG_LOAD_SUCCESS);
    }


    public ArrayList<PickerJsonBean> parseData(String result) {//Gson 解析
        ArrayList<PickerJsonBean> detail = new ArrayList<>();
        try {
            JSONArray data = new JSONArray(result);
            Gson gson = new Gson();
            for (int i = 0; i < data.length(); i++) {
                PickerJsonBean entity = gson.fromJson(data.optJSONObject(i).toString(), PickerJsonBean.class);
                detail.add(entity);
            }
        } catch (Exception e) {
            e.printStackTrace();
            mHandler.sendEmptyMessage(MSG_LOAD_FAILED);
        }
        return detail;
    }

    @Override
    public int getId() {
        return addressBean == null ? -1 : addressBean.getId();
    }

    @Override
    public String getName() {
        return binding.etName.getText().toString();
    }

    @Override
    public String getPhone() {
        return binding.etPhone.getText().toString();
    }

    @Override
    public String getProvinces() {
        return province;
    }

    @Override
    public String getCity() {
        return city;
    }

    @Override
    public String getArea() {
        return area;
    }

    @Override
    public String getDetail() {
        return binding.detailAddressView.getText().toString();
    }

    @Override
    public int getIsDefault() {
        return binding.isDefaultView.isChecked() ? 1 : 0;
    }

    @Override
    public void updateSuccess() {
        if (addressBean == null) {
            addressBean = new AddressBean();
        }
        addressBean.setName(getName());
        addressBean.setPhone(getPhone());
        addressBean.setProvinces(getProvinces());
        addressBean.setCity(getCity());
        addressBean.setArea(getArea());
        addressBean.setDetail(getDetail());

        Intent intent = new Intent();
        Bundle bundle = new Bundle();
        bundle.putParcelable(ADDRESS, addressBean);
        intent.putExtras(bundle);
        setResult(RESULT_OK, intent);
        finish();
    }

}
