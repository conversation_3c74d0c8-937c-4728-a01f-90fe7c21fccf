package com.dep.biguo.mvp.ui.adapter;

import android.graphics.Paint;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.Nullable;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.utils.image.ImageLoader;
import java.util.List;

public class SecondHandProductAdapter extends BaseQuickAdapter<ShopBean, BaseViewHolder> {

    public SecondHandProductAdapter(@Nullable List<ShopBean> data) {
        super(R.layout.item_second_hand_product, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ShopBean item) {
        if (item == null) {
            return;
        }

        ImageView ivProductImage = helper.getView(R.id.ivProductImage);
        if (item.getImg() != null && !item.getImg().isEmpty()) {
            ImageLoader.loadImage(ivProductImage, item.getImg());
        } else {
            ivProductImage.setImageResource(R.drawable.bg_round_8_gray);
        }

        helper.setText(R.id.tvProductTitle, item.getName());
        helper.setText(R.id.tvPrice, "¥" + item.getPrice());

        TextView tvOriginalPrice = helper.getView(R.id.tvOriginalPrice);
        tvOriginalPrice.setText("¥" + item.getPreferential_price());
        tvOriginalPrice.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);

        ImageView ivSellTag = helper.getView(R.id.ivSellTag);
        ivSellTag.setVisibility(item.isSold() ? View.VISIBLE : View.GONE);
    }
}