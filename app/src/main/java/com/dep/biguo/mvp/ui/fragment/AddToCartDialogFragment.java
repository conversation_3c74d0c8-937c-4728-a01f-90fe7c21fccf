package com.dep.biguo.mvp.ui.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import com.dep.biguo.R;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.google.android.material.chip.ChipGroup;

public class AddToCartDialogFragment extends BottomSheetDialogFragment {

    public static final String TAG = "AddToCartDialogFragment";

    private RecyclerView rvColors;
    private ChipGroup cgPackages;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_add_to_cart, container, false);
        rvColors = view.findViewById(R.id.rv_colors);
        cgPackages = view.findViewById(R.id.cg_packages);
        // TODO: Setup RecyclerView for colors and ChipGroup for packages
        return view;
    }
}