package com.dep.biguo.mvp.ui.activity;

import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.dep.biguo.R;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.databinding.SecondHandActivityBinding;
import com.dep.biguo.mvp.ui.adapter.LatestReleaseAdapter;
import com.dep.biguo.mvp.ui.adapter.SecondHandProductAdapter;
import com.google.android.material.tabs.TabLayout;
import com.youth.banner.adapter.BannerImageAdapter;
import com.youth.banner.holder.BannerImageHolder;
import com.youth.banner.indicator.CircleIndicator;

import java.util.ArrayList;
import java.util.List;

public class SecondHandActivity extends AppCompatActivity implements View.OnClickListener {

    private SecondHandActivityBinding binding;
    private LatestReleaseAdapter latestReleaseAdapter;
    private SecondHandProductAdapter allSecondHandAdapter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.second_hand_activity);
        binding.setOnClickListener(this);

        setStatusBarColorGradientStart();
        setupBanner();
        setupTabs();
        initRecyclerViews();
        loadMockData();
    }


    private void setStatusBarColorGradientStart() {
        Window window = getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            );
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    private void setupBanner() {
        List<Integer> bannerImages = new ArrayList<>();
        bannerImages.add(R.drawable.bg_gradient_red);
        bannerImages.add(R.drawable.bg_gradient_red);
        bannerImages.add(R.drawable.bg_gradient_red);

        binding.banner.setAdapter(new BannerImageAdapter<Integer>(bannerImages) {
            @Override
            public void onBindView(BannerImageHolder holder, Integer data, int position, int size) {
                holder.imageView.setImageResource(data);
            }
        }).setIndicator(new CircleIndicator(this));
    }

    private void setupTabs() {
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText("学习工具"));
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText("二手教材"));
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText("助农项目"));
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText("周边产品"));
        // Set "二手教材" as selected
        binding.tabLayout.getTabAt(1).select();
    }

    private void initRecyclerViews() {
        // Latest Release
        latestReleaseAdapter = new LatestReleaseAdapter(new ArrayList<>());
        binding.rvLatestRelease.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
        binding.rvLatestRelease.setAdapter(latestReleaseAdapter);

        // All Second Hand Products
        allSecondHandAdapter = new SecondHandProductAdapter(new ArrayList<>());
        binding.rvAllSecondHand.setLayoutManager(new GridLayoutManager(this, 2));
        binding.rvAllSecondHand.setAdapter(allSecondHandAdapter);
    }

    private void loadMockData() {
        // Mock Latest Release Data
        List<ShopBean> latestReleaseProducts = new ArrayList<>();
        latestReleaseProducts.add(new ShopBean(1, "全新马克思主义教...", "35.00"));
        latestReleaseProducts.add(new ShopBean(2, "二手马克思原理二", "16.00"));
        latestReleaseProducts.add(new ShopBean(3, "全新英语二教材", "39.00"));
        latestReleaseAdapter.setNewData(latestReleaseProducts);

        // Mock All Second Hand Products Data
        List<ShopBean> allSecondHandProducts = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            ShopBean bean = new ShopBean();
            bean.setId(i + 10);
            bean.setName("用户上传商品名称最多展示两行文字");
            bean.setPrice("30.00");
            bean.setPreferential_price("40.00");
            bean.setSold(i % 3 == 0); // Some items are marked as sold
            allSecondHandProducts.add(bean);
        }
        allSecondHandAdapter.setNewData(allSecondHandProducts);
    }


    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.ivShoppingCart) {
            // TODO: Handle shopping cart click
        }
    }
} 