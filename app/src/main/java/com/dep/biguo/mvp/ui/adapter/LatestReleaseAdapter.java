package com.dep.biguo.mvp.ui.adapter;

import android.widget.ImageView;
import androidx.annotation.Nullable;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.utils.image.ImageLoader;
import java.util.List;

public class LatestReleaseAdapter extends BaseQuickAdapter<ShopBean, BaseViewHolder> {

    public LatestReleaseAdapter(@Nullable List<ShopBean> data) {
        super(R.layout.item_latest_release, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ShopBean item) {
        if (item == null) {
            return;
        }

        ImageView ivProductImage = helper.getView(R.id.ivProductImage);
        if (item.getImg() != null && !item.getImg().isEmpty()) {
            ImageLoader.loadImage(ivProductImage, item.getImg());
        } else {
            ivProductImage.setImageResource(R.drawable.bg_round_8_gray);
        }

        helper.setText(R.id.tvProductTitle, item.getName());
        helper.setText(R.id.tvPrice, "¥" + item.getPrice());
    }
}