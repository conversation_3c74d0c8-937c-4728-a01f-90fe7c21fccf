package com.dep.biguo.mvp.model.entity;

import java.util.List;

public class Review {
    private String avatarUrl;
    private String nickname;
    private float rating;
    private String content;
    private List<String> imageUrls;

    public Review(String avatarUrl, String nickname, float rating, String content, List<String> imageUrls) {
        this.avatarUrl = avatarUrl;
        this.nickname = nickname;
        this.rating = rating;
        this.content = content;
        this.imageUrls = imageUrls;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public float getRating() {
        return rating;
    }

    public void setRating(float rating) {
        this.rating = rating;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(List<String> imageUrls) {
        this.imageUrls = imageUrls;
    }
}