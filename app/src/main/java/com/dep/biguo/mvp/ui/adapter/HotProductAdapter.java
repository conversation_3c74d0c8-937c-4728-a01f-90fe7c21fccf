package com.dep.biguo.mvp.ui.adapter;

import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.utils.image.ImageLoader;

import java.util.List;

/**
 * 热卖商品适配器
 */
public class HotProductAdapter extends BaseQuickAdapter<ShopBean, BaseViewHolder> {

    public HotProductAdapter(@Nullable List<ShopBean> data) {
        super(R.layout.item_hot_product, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, ShopBean item) {
        if (item == null) {
            return;
        }

        // 商品图片
        ImageView ivProductImage = helper.getView(R.id.ivProductImage);
        if (item.getImg() != null && !item.getImg().isEmpty()) {
            ImageLoader.loadImage(ivProductImage, item.getImg());
        } else {
            ivProductImage.setImageResource(R.drawable.bg_round_8_gray);
        }

        // 商品标题
        helper.setText(R.id.tvProductTitle, item.getName());

        // 价格
        helper.setText(R.id.tvPrice, "¥" + item.getPrice());

        // 销量排行
        TextView tvTopSale = helper.getView(R.id.tvTopSale);
        int position = helper.getAdapterPosition();
        tvTopSale.setText("月销量TOP" + (position + 1));
    }
}