package com.dep.biguo.mvp.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.dep.biguo.R;
import com.dep.biguo.mvp.model.entity.Review; // Assuming a Review model exists
import java.util.List;

public class ReviewAdapter extends RecyclerView.Adapter<ReviewAdapter.ViewHolder> {

    private Context context;
    private List<Review> reviewList;

    public ReviewAdapter(Context context, List<Review> reviewList) {
        this.context = context;
        this.reviewList = reviewList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_product_review, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Review review = reviewList.get(position);
        holder.tvNickname.setText(review.getNickname());
        holder.tvReviewContent.setText(review.getContent());
        holder.ratingBar.setRating(review.getRating());
        // TODO: Load avatar and review images using a library like Glide or Picasso
    }

    @Override
    public int getItemCount() {
        return reviewList.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView ivAvatar;
        TextView tvNickname;
        RatingBar ratingBar;
        TextView tvReviewContent;
        RecyclerView rvReviewImages;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivAvatar = itemView.findViewById(R.id.iv_avatar);
            tvNickname = itemView.findViewById(R.id.tv_nickname);
            ratingBar = itemView.findViewById(R.id.rating_bar);
            tvReviewContent = itemView.findViewById(R.id.tv_review_content);
            rvReviewImages = itemView.findViewById(R.id.rv_review_images);
        }
    }
}