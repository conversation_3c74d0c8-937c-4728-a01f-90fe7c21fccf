package com.dep.biguo.mvp.ui.activity;

import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.viewpager.widget.ViewPager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import com.dep.biguo.R;
import com.dep.biguo.mvp.model.entity.Review;
import com.dep.biguo.mvp.ui.adapter.ProductImageAdapter;
import com.dep.biguo.mvp.ui.adapter.ReviewAdapter;
import com.dep.biguo.mvp.ui.fragment.AddToCartDialogFragment;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ProductDetailActivity extends AppCompatActivity {

    private Toolbar toolbar;
    private ViewPager vpProductImages;
    private TextView tvProductPrice;
    private TextView tvProductName;
    private TextView tvProductDescription;
    private RecyclerView rvReviews;
    private ImageView ivProductDetails;
    private TextView tvCustomerService;
    private Button btnAddToCart;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_product_detail);

        toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("商品详情");

        vpProductImages = findViewById(R.id.vp_product_images);
        tvProductPrice = findViewById(R.id.tv_product_price);
        tvProductName = findViewById(R.id.tv_product_name);
        tvProductDescription = findViewById(R.id.tv_product_description);
        rvReviews = findViewById(R.id.rv_reviews);
        ivProductDetails = findViewById(R.id.iv_product_details);
        tvCustomerService = findViewById(R.id.tv_customer_service);
        btnAddToCart = findViewById(R.id.btn_add_to_cart);

        btnAddToCart.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AddToCartDialogFragment addToCartDialogFragment = new AddToCartDialogFragment();
                addToCartDialogFragment.show(getSupportFragmentManager(), AddToCartDialogFragment.TAG);
            }
        });

        setupDummyData();
    }

    private void setupDummyData() {
        // Dummy Product Images
        List<String> productImages = new ArrayList<>(Arrays.asList("url1", "url2", "url3", "url4"));
        ProductImageAdapter productImageAdapter = new ProductImageAdapter(this, productImages);
        vpProductImages.setAdapter(productImageAdapter);

        // Dummy Reviews
        List<Review> reviewList = new ArrayList<>();
        List<String> images1 = new ArrayList<>();
        // In a real app, you would load these from your resources or a URL
        // images1.add("some_image_url_or_resource");
        reviewList.add(new Review("avatar_url", "知有", 5, "真是太好用了，大大提高了学习效率。", images1));

        List<String> images2 = new ArrayList<>();
        reviewList.add(new Review("avatar_url_2", "小菠萝", 4, "这里是对商品的评价内容，这里是对商品的评价内容这里是对商品的评价内容。", images2));


        ReviewAdapter reviewAdapter = new ReviewAdapter(this, reviewList);
        rvReviews.setLayoutManager(new LinearLayoutManager(this));
        rvReviews.setAdapter(reviewAdapter);
        rvReviews.setNestedScrollingEnabled(false);

        // Dummy product details image
        // In a real app, load this with Glide/Picasso
        // ivProductDetails.setImageResource(R.drawable.some_detail_image);
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}