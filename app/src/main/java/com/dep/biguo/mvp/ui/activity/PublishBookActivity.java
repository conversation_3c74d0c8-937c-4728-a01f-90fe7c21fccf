package com.dep.biguo.mvp.ui.activity;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.dep.biguo.R;
import com.dep.biguo.databinding.PublishBookActivityBinding;
import com.dep.biguo.mvp.ui.adapter.SelectedImageAdapter;

import java.util.ArrayList;
import java.util.List;

public class PublishBookActivity extends AppCompatActivity implements View.OnClickListener {

    private PublishBookActivityBinding binding;
    private SelectedImageAdapter imageAdapter;
    private List<String> selectedImages = new ArrayList<>();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.publish_book_activity);
        binding.setOnClickListener(this);

        setStatusBarColor();
        initImageRecyclerView();
    }

    private void setStatusBarColor() {
        Window window = getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.WHITE);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            }
        }
    }

    private void initImageRecyclerView() {
        // Add a placeholder for the "add" button
        selectedImages.add("add_button_placeholder");
        imageAdapter = new SelectedImageAdapter(selectedImages);
        binding.rvImages.setLayoutManager(new LinearLayoutManager(this, RecyclerView.HORIZONTAL, false));
        binding.rvImages.setAdapter(imageAdapter);

        imageAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            if (view.getId() == R.id.ivDelete) {
                selectedImages.remove(position);
                imageAdapter.notifyItemRemoved(position);
            }
        });

        imageAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (selectedImages.get(position).equals("add_button_placeholder")) {
                // TODO: Implement image selection logic
                Toast.makeText(this, "Add image clicked", Toast.LENGTH_SHORT).show();
            }
        });
    }


    @Override
    public void onClick(View v) {
        int id = v.getId();

        if (id == R.id.ivBack) {
            finish();
        } else if (id == R.id.btnPublish) {
            publishBook();
        }
    }

    private void publishBook() {
        String bookName = binding.etBookName.getText().toString().trim();
        String isbn = binding.etIsbn.getText().toString().trim();
        String purchasePrice = binding.etPurchasePrice.getText().toString().trim();
        String sellingPrice = binding.etSellingPrice.getText().toString().trim();
        String description = binding.etDescription.getText().toString().trim();

        if (bookName.isEmpty() || isbn.isEmpty() || purchasePrice.isEmpty() || sellingPrice.isEmpty() || description.isEmpty()) {
            Toast.makeText(this, "Please fill all fields", Toast.LENGTH_SHORT).show();
            return;
        }

        if (!binding.cbTerms.isChecked()) {
            Toast.makeText(this, "Please agree to the terms", Toast.LENGTH_SHORT).show();
            return;
        }

        // TODO: Implement actual publish logic
        Toast.makeText(this, "Published successfully!", Toast.LENGTH_SHORT).show();
        finish();
    }
}