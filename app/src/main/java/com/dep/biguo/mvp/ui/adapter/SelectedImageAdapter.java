package com.dep.biguo.mvp.ui.adapter;

import android.view.View;
import android.widget.ImageView;
import androidx.annotation.Nullable;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.utils.image.ImageLoader;
import java.util.List;

public class SelectedImageAdapter extends BaseQuickAdapter<String, BaseViewHolder> {

    private static final int VIEW_TYPE_IMAGE = 1;
    private static final int VIEW_TYPE_ADD = 2;

    public SelectedImageAdapter(@Nullable List<String> data) {
        super(data);
        addItemType(VIEW_TYPE_IMAGE, R.layout.item_selected_image);
        addItemType(VIEW_TYPE_ADD, R.layout.item_add_image);
    }

    @Override
    protected void convert(BaseViewHolder helper, String item) {
        switch (helper.getItemViewType()) {
            case VIEW_TYPE_IMAGE:
                ImageView ivImage = helper.getView(R.id.ivImage);
                ImageLoader.loadImage(ivImage, item);
                helper.addOnClickListener(R.id.ivDelete);
                break;
            case VIEW_TYPE_ADD:
                // No data binding needed for the "add" button
                break;
        }
    }

    @Override
    public int getDefItemViewType(int position) {
        if (mData.get(position).equals("add_button_placeholder")) {
            return VIEW_TYPE_ADD;
        }
        return VIEW_TYPE_IMAGE;
    }
}