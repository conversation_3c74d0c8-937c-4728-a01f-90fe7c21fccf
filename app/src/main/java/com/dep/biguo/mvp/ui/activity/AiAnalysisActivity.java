package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.KeyboardUtils;
import com.dep.biguo.R;
import com.dep.biguo.bean.AiAnalysisBean;
import com.dep.biguo.databinding.AiAnalysisActivityBinding;
import com.dep.biguo.di.component.DaggerAiAnalysisComponent;
import com.dep.biguo.mvp.contract.AiAnalysisContract;
import com.dep.biguo.mvp.presenter.AiAnalysisPresenter;
import com.dep.biguo.mvp.ui.adapter.AiAnalysisAdapter;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.widget.ItemDecoration;
import com.google.gson.reflect.TypeToken;
import com.jess.arms.di.component.AppComponent;

import java.util.Map;

public class AiAnalysisActivity extends BaseLoadSirActivity<AiAnalysisPresenter> implements AiAnalysisContract.View, View.OnClickListener {
    private static final String AI_PARAMS = "AiParams";
    private static final String QUESTIONING = "questioning";

    private AiAnalysisActivityBinding binding;
    private AiAnalysisAdapter adapter = new AiAnalysisAdapter();

    public static void Start(Context context, Map<String, Object> map, String questioning){
        Intent intent = new Intent(context, AiAnalysisActivity.class);
        intent.putExtra(AI_PARAMS, GsonUtils.toJson(map));
        intent.putExtra(QUESTIONING, questioning);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerAiAnalysisComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.ai_analysis_activity);
        binding.setOnClickListener(this);
        //软键盘打开的时候，布局不要顶出屏幕
        KeyboardUtils.setShowKeyboardScroll(this, binding.getRoot(), true);

        adapter = new AiAnalysisAdapter();
        adapter.setOnReloadListener(item -> {
            if(item.getOutput() != null){
                readSuccess(item);
            }
            mPresenter.request(item);
        });
        binding.recyclerView.setAdapter(adapter);
        binding.recyclerView.setItemAnimator(null);
        binding.recyclerView.addItemDecoration(new ItemDecoration(ItemDecoration.Horizontal));

        //滚动的时候关闭软键盘
        binding.recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                KeyboardUtils.hideKeyboard(binding.inputView);
            }
        });

        //当点击输入框的时候，滚动到列表的底部
        binding.inputView.setOnTouchClickListener(() -> {
            binding.inputView.postDelayed(() -> scrollBottom(), 100);
        });
        return 0;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        Map<String, Object> map = GsonUtils.fromJson(getIntent().getStringExtra(AI_PARAMS), new TypeToken<Map<String, Object>>(){}.getType());
        mPresenter.setMap(map);
        createAiBean("");
    }

    @Override
    public void onRequest() {

    }

    public boolean createAiBean(String question){
        if(adapter.getItemCount() > 0 && adapter.getItem(adapter.getItemCount() - 1).getStatus() == 0) return false;

        //如果有上一次的提问，就取用上一次的session_id才能连续回答
        AiAnalysisBean lastBean = adapter.getItem(adapter.getItemCount() - 1);
        AiAnalysisBean.OutPut outPut = new AiAnalysisBean.OutPut();
        outPut.setFinish_reason("");
        outPut.setSession_id(lastBean != null ? lastBean.getOutput().getSession_id() : "");
        //创建一轮对话
        AiAnalysisBean bean = new AiAnalysisBean();
        bean.setStatus(0);
        bean.setQuestion(question);
        bean.setOutput(outPut);
        bean.setResult_code(1);
        bean.setOver_times(0);
        bean.setFree_times(0);
        //添加到列表中，并滚动到底部，关闭软键盘
        adapter.addData(bean);
        scrollBottom();
        KeyboardUtils.hideKeyboard(binding.inputView);
        //思考中就把发送按钮变为半透明，表示不可提问
        binding.sendView.setAlpha(0.5f);

        mPresenter.request(bean);
        return true;
    }

    @Override
    public void onClick(View view) {
        if(view == binding.closeView || view == binding.getRoot()){
            finish();

        }else if(view == binding.sendView){
            if(AppUtil.isEmpty(binding.inputView.getText().toString())) return;

            if(createAiBean(binding.inputView.getText().toString())){
                binding.inputView.setText("");
            }
        }
    }

    public void scrollBottom(){
        //滚动到最底部
        int height = binding.recyclerView.computeVerticalScrollRange();
        binding.recyclerView.scrollBy(0, height);
    }

    @Override
    public void readSuccess(AiAnalysisBean item) {
        int index = adapter.getData().indexOf(item);
        adapter.notifyItemChanged(index);
        scrollBottom();
        //思考中就把发送按钮变为半透明，表示不可提问
        binding.sendView.setAlpha(item.getStatus() == 0 ? 0.5f : 1f);

        readLimitation(item);
    }

    public void readLimitation(AiAnalysisBean item) {
        int free_times = item.getFree_times();
        int over_times = item.getOver_times();
        boolean isSuccess = item.getResult_code() == 1;
        if(isSuccess && !UserCache.isMemberShip() && free_times > 0 && over_times > 0){
            binding.freeCountView.setText(String.format("（免费试用%s次）", free_times));
        }else if(isSuccess && !UserCache.isMemberShip() && free_times > 0 &&  over_times <= 0){
            binding.freeCountView.setText(String.format("（免费试用%s次已用完）", free_times));
        }else {
            binding.freeCountView.setText("");
        }
    }

    @Override
    public Context getContext() {
        return this;
    }

}