<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="80dp"
    android:layout_height="80dp"
    android:layout_marginEnd="8dp">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardCornerRadius="8dp">

        <ImageView
            android:id="@+id/ivImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

    </androidx.cardview.widget.CardView>

    <ImageView
        android:id="@+id/ivDelete"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_gravity="top|end"
        android:src="@drawable/icon_delete"
        android:background="@drawable/bg_circle_black_alpha"
        android:padding="4dp"
        app:tint="@color/white"/>

</FrameLayout>
