<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.PublishBookActivity" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <!-- Top Bar -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize">

            <ImageView
                android:id="@+id/ivBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="16dp"
                android:onClick="@{onClickListener.onClick}"
                android:src="@drawable/arrow_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="发布闲置"
                android:textColor="@color/tblack"
                android:textSize="18sp"
                android:textStyle="bold" />

        </RelativeLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Image/Video Upload -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvImages"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:listitem="@layout/item_selected_image"
                    tools:itemCount="3"/>

                <!-- Textbook Name -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="教材名称"
                    android:textColor="@color/tblack"
                    android:textSize="16sp" />

                <EditText
                    android:id="@+id/etBookName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/bg_edittext_gray"
                    android:hint="马克思教材二"
                    android:padding="12dp"
                    android:textSize="14sp" />

                <!-- ISBN -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="国际标准书号 (ISBN)"
                        android:textColor="@color/tblack"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_marginStart="4dp"
                        android:src="@drawable/icon_help" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/bg_toast_gray"
                        android:padding="4dp"
                        android:text="书本背面二维码上方以ISBN开头的数字哦"
                        android:textColor="@color/tblack2"
                        android:textSize="10sp" />
                </LinearLayout>

                <EditText
                    android:id="@+id/etIsbn"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/bg_edittext_gray"
                    android:hint="请填写国际标准字号"
                    android:padding="12dp"
                    android:textSize="14sp" />

                <!-- Condition -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="教材新旧程度"
                    android:textColor="@color/tblack"
                    android:textSize="16sp" />

                <Spinner
                    android:id="@+id/spinnerCondition"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/bg_spinner_gray"
                    android:padding="12dp" />

                <!-- Purchase Price -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="购入价格"
                    android:textColor="@color/tblack"
                    android:textSize="16sp" />

                <EditText
                    android:id="@+id/etPurchasePrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/bg_edittext_gray"
                    android:hint="请填写购入价格"
                    android:inputType="numberDecimal"
                    android:padding="12dp"
                    android:textSize="14sp" />

                <!-- Selling Price -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="定价"
                    android:textColor="@color/tblack"
                    android:textSize="16sp" />

                <EditText
                    android:id="@+id/etSellingPrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/bg_edittext_gray"
                    android:hint="¥16.00"
                    android:inputType="numberDecimal"
                    android:padding="12dp"
                    android:textSize="14sp" />

                <!-- Description -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="教材描述"
                    android:textColor="@color/tblack"
                    android:textSize="16sp" />

                <EditText
                    android:id="@+id/etDescription"
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/bg_edittext_gray"
                    android:gravity="top"
                    android:hint="描述一下教材的状态、来源..."
                    android:maxLength="100"
                    android:padding="12dp"
                    android:textSize="14sp" />

                <!-- Terms and Conditions -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:gravity="center_vertical">

                    <CheckBox
                        android:id="@+id/cbTerms"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="我已知晓 温馨提示：闲置商品需要卖家承担运费在48小时内自行发货，否则将会自动取消订单，交易成功后平台收取1.00元服务费。"
                        android:textColor="@color/tblack2"
                        android:textSize="12sp" />
                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <!-- Publish Button -->
        <Button
            android:id="@+id/btnPublish"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@drawable/bg_button_red"
            android:onClick="@{onClickListener.onClick}"
            android:text="发布闲置"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </LinearLayout>
</layout>