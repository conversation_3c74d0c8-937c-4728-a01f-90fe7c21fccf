<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    tools:context=".mvp.ui.activity.ProductDetailActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Product Image Carousel -->
            <androidx.viewpager.widget.ViewPager
                android:id="@+id/vp_product_images"
                android:layout_width="match_parent"
                android:layout_height="300dp" />

            <!-- Product Info -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:id="@+id/tv_product_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="¥2500.00"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="24sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_product_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="笔果AI学习机-基础版"
                    android:textColor="@android:color/black"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/tv_product_description"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="这是对商品的描述。这是对商品的描述这是对商品的描述这是对商品的描述，这是对商品的描述这是对商品的描述。" />

            </LinearLayout>

            <!-- Buyer Reviews -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="买家评价 (999)"
                    android:textColor="@android:color/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_reviews"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp" />

            </LinearLayout>

            <!-- Product Details -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="详情介绍"
                    android:textColor="@android:color/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_product_details"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:scaleType="centerCrop" />

            </LinearLayout>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@android:color/white"
        android:orientation="horizontal"
        android:padding="8dp">

        <TextView
            android:id="@+id/tv_customer_service"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:drawableTop="@drawable/icon_help"
            android:gravity="center"
            android:text="客服" />

        <Button
            android:id="@+id/btn_add_to_cart"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:background="@drawable/bg_add_to_cart_button"
            android:text="加入购物车"
            android:textColor="@android:color/white" />
    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>