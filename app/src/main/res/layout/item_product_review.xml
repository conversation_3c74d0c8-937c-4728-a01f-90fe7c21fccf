<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_avatar"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:scaleType="centerCrop" />

        <TextView
            android:id="@+id/tv_nickname"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:text="User Nickname"
            android:textColor="@android:color/black" />

        <RatingBar
            android:id="@+id/rating_bar"
            style="?android:attr/ratingBarStyleSmall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:numStars="5"
            android:rating="5" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_review_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="This is a review of the product." />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_review_images"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp" />

</LinearLayout>