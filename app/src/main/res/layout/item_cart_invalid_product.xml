<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="8dp">

    <ImageView
        android:id="@+id/iv_product_image"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginStart="8dp"
        android:alpha="0.5"
        android:scaleType="centerCrop" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_product_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Product Name"
            android:textColor="@android:color/darker_gray" />

        <TextView
            android:id="@+id/tv_product_spec"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Product Specification"
            android:textColor="@android:color/darker_gray" />

        <TextView
            android:id="@+id/tv_product_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="¥30.00"
            android:textColor="@android:color/darker_gray" />
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="商品失效"
        android:textColor="@android:color/darker_gray" />

</LinearLayout>