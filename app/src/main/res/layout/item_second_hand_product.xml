<![CDATA[
<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="6dp"
    android:layout_marginEnd="6dp"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="0dp"
    app:cardBackgroundColor="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="160dp">

            <ImageView
                android:id="@+id/ivProductImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                tools:src="@drawable/bg_round_8_gray" />

            <ImageView
                android:id="@+id/ivSellTag"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="bottom|end"
                android:layout_margin="8dp"
                android:src="@drawable/icon_sell_tag"
                android:visibility="gone"
                tools:visibility="visible"/>

        </FrameLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvProductTag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_product_tag"
                    android:paddingStart="5dp"
                    android:paddingTop="2dp"
                    android:paddingEnd="5dp"
                    android:paddingBottom="2dp"
                    android:text="二手"
                    android:textColor="@color/red"
                    android:textSize="10sp" />

                <TextView
                    android:id="@+id/tvProductTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textColor="@color/tblack"
                    android:textSize="14sp"
                    tools:text="用户上传商品名称最多展示两行文字" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="bottom">

                <TextView
                    android:id="@+id/tvPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/red"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    tools:text="¥30.00" />

                <TextView
                    android:id="@+id/tvOriginalPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:foreground="?android:attr/listChoiceIndicatorSingle"
                    android:textColor="@color/tblack3"
                    android:textSize="12sp"
                    tools:text="¥40.00" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
]]>