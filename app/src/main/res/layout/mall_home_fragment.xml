<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.fragment.MallHomeFragment" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:fitsSystemWindows="false"
        android:orientation="vertical">

        <!-- 顶部区域 -->
        <LinearLayout
            android:id="@+id/mallHomeTopLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="15dp">

            <com.dep.biguo.widget.StatusBarView
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <!-- 标题和购物车 -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:text="商城"
                    android:textColor="@color/tblack"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/ivShoppingCart"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:src="@drawable/icon_shopping_cart" />

            </RelativeLayout>

            <!-- 搜索框 -->
            <EditText
                android:id="@+id/etSearch"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="15dp"
                android:background="@drawable/bg_round_50_gray_f5"
                android:drawableStart="@drawable/icon_search_gray"
                android:drawablePadding="8dp"
                android:hint="搜索关键词，查询题目"
                android:imeOptions="actionSearch"
                android:paddingStart="15dp"
                android:paddingEnd="15dp"
                android:singleLine="true"
                android:textColorHint="@color/tblack3"
                android:textSize="14sp" />

        </LinearLayout>

        <com.dep.biguo.widget.SmartRefreshLayout
            android:id="@+id/swipeLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@color/bg_gray_f5">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fillViewport="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- 横幅广告 -->
                    <com.youth.banner.Banner
                        android:id="@+id/banner"
                        android:layout_width="match_parent"
                        android:layout_height="140dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        app:banner_radius="8dp" />

                    <!-- 分类Tab -->
                    <com.google.android.material.tabs.TabLayout
                        android:id="@+id/tabLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        app:tabGravity="start"
                        app:tabIndicatorColor="@color/red"
                        app:tabIndicatorHeight="3dp"
                        app:tabMode="scrollable"
                        app:tabSelectedTextColor="@color/tblack"
                        app:tabTextColor="@color/tblack2" />

                    <!-- 热卖商品 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/bg_round_8_white"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:orientation="vertical"
                        android:padding="12dp">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:drawableStart="@drawable/icon_hot_sale"
                                android:drawablePadding="4dp"
                                android:gravity="center_vertical"
                                android:text="热卖商品"
                                android:textColor="@color/tblack"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvHotSaleMore"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:text="···"
                                android:textColor="@color/tblack3"
                                android:textSize="16sp" />

                        </RelativeLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rvHotProducts"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:orientation="horizontal"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                    </LinearLayout>

                    <!-- 全部商品 -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginBottom="12dp"
                        android:text="全部商品"
                        android:textColor="@color/tblack"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvAllProducts"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:nestedScrollingEnabled="false"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="2" />

                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

        </com.dep.biguo.widget.SmartRefreshLayout>

    </LinearLayout>

</layout>
