<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_cart_items"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/app_bar"
        android:layout_above="@+id/bottom_bar"
        android:padding="8dp" />

    <LinearLayout
        android:id="@+id/bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@android:color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <CheckBox
            android:id="@+id/cb_select_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="全选" />

        <TextView
            android:id="@+id/tv_total_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end"
            android:text="合计: ¥2699.00"
            android:textColor="@android:color/holo_red_dark"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_checkout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:background="@drawable/bg_add_to_cart_button"
            android:text="去结算(2)"
            android:textColor="@android:color/white" />
    </LinearLayout>

</RelativeLayout>